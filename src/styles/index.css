@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .solid-card {
    background: #ffffff;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  }

  .solid-card-elevated {
    background: #ffffff;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.12);
  }

  .hover-lift {
    transition: all 0.2s ease;
    will-change: transform, box-shadow;
  }

  .hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: 0 16px 64px rgba(0, 0, 0, 0.15);
  }

  .primary-text {
    color: #1e40af;
    font-weight: 600;
  }

  .fade-in {
    animation: fadeIn 0.4s ease-out;
  }

  .slide-up {
    animation: slideUp 0.4s ease-out;
  }

  .textured-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    min-height: 100vh;
  }

  /* 主题颜色系统 */
  .theme-blue {
    --primary-color: #3b82f6;
    --primary-light: #dbeafe;
    --primary-dark: #1e40af;
    --gradient-from: #3b82f6;
    --gradient-to: #1e40af;
  }

  .theme-purple {
    --primary-color: #8b5cf6;
    --primary-light: #ede9fe;
    --primary-dark: #6d28d9;
    --gradient-from: #8b5cf6;
    --gradient-to: #6d28d9;
  }

  .theme-green {
    --primary-color: #10b981;
    --primary-light: #d1fae5;
    --primary-dark: #047857;
    --gradient-from: #10b981;
    --gradient-to: #047857;
  }

  .theme-orange {
    --primary-color: #f59e0b;
    --primary-light: #fef3c7;
    --primary-dark: #d97706;
    --gradient-from: #f59e0b;
    --gradient-to: #d97706;
  }

  /* 动态主题应用 */
  .card-themed {
    border-color: var(--primary-color);
  }

  .bg-themed {
    background: linear-gradient(135deg, var(--gradient-from), var(--gradient-to));
  }

  .text-themed {
    color: var(--primary-color);
  }

  .bg-themed-light {
    background-color: var(--primary-light);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* 新增动画效果 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6);
  }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

.glow-animation {
  animation: glow 2s ease-in-out infinite;
}

/* 渐变文字效果 */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 改进的卡片样式 */
.solid-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.solid-card-elevated {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}
