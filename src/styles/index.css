@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .solid-card {
    background: #ffffff;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  }

  .solid-card-elevated {
    background: #ffffff;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.12);
  }

  .hover-lift {
    transition: all 0.2s ease;
    will-change: transform, box-shadow;
  }

  .hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: 0 16px 64px rgba(0, 0, 0, 0.15);
  }

  .primary-text {
    color: #1e40af;
    font-weight: 600;
  }

  .fade-in {
    animation: fadeIn 0.4s ease-out;
  }

  .slide-up {
    animation: slideUp 0.4s ease-out;
  }

  .textured-bg {
    background: #f8fafc;
    background-image:
      radial-gradient(circle at 1px 1px, rgba(30, 64, 175, 0.03) 1px, transparent 0);
    background-size: 20px 20px;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
