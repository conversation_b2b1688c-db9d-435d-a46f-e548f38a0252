import React, { useState, useEffect, useCallback, useMemo, memo } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import ChapterCard from '../components/ChapterCard'
import coursesData from '../data/courses.json'

const ChapterPage = memo(() => {
  const { courseId } = useParams()
  const navigate = useNavigate()
  const [selectedChapter, setSelectedChapter] = useState(null)

  const course = useMemo(() => {
    return coursesData.courses.find(c => c.id === parseInt(courseId))
  }, [courseId])

  // 获取主题颜色配置
  const getThemeColors = (theme) => {
    const themes = {
      blue: {
        primary: 'bg-blue-600',
        light: 'bg-blue-50',
        border: 'border-blue-600',
        text: 'text-blue-600',
        hover: 'hover:border-blue-300'
      },
      purple: {
        primary: 'bg-purple-600',
        light: 'bg-purple-50',
        border: 'border-purple-600',
        text: 'text-purple-600',
        hover: 'hover:border-purple-300'
      },
      green: {
        primary: 'bg-green-600',
        light: 'bg-green-50',
        border: 'border-green-600',
        text: 'text-green-600',
        hover: 'hover:border-green-300'
      },
      orange: {
        primary: 'bg-orange-600',
        light: 'bg-orange-50',
        border: 'border-orange-600',
        text: 'text-orange-600',
        hover: 'hover:border-orange-300'
      }
    }
    return themes[theme] || themes.blue
  }

  const themeColors = course ? getThemeColors(course.theme) : getThemeColors('blue')

  useEffect(() => {
    if (course && course.chapters.length > 0) {
      setSelectedChapter(course.chapters[0]) // 默认选择第一个章节
    }
  }, [course])

  const handleChapterSelect = useCallback((chapter) => {
    setSelectedChapter(chapter)
  }, [])

  const handleBackToHome = useCallback(() => {
    navigate('/')
  }, [navigate])

  if (!course) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex flex-col lg:flex-row bg-slate-50">
      {/* 左侧：课程卡片区域 */}
      <div className="lg:w-80 lg:min-h-screen bg-white border-r border-slate-200 fade-in">
        <div className="p-6 h-full overflow-y-auto scrollbar-hide">
          {/* 返回按钮 */}
          <button
            onClick={handleBackToHome}
            className={`flex items-center space-x-3 ${themeColors.text} hover:bg-slate-50 px-4 py-3 rounded-lg mb-6 transition-all duration-200 hover-lift border border-slate-200 hover:border-slate-300 w-full justify-center font-medium`}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            <span>返回首页</span>
          </button>

          {/* 课程信息卡片 */}
          <div className={`solid-card p-6 mb-6 slide-up border-t-4 ${themeColors.border}`} style={{ animationDelay: '50ms' }}>
            <div className={`aspect-square ${themeColors.primary} rounded-lg mb-4 flex items-center justify-center`}>
              <div className="text-white text-2xl font-bold">
                {course.title.charAt(0)}
              </div>
            </div>
            <div className={`px-2 py-1 ${themeColors.light} rounded text-xs font-medium ${themeColors.text} mb-3 inline-block`}>
              {course.level}
            </div>
            <h2 className="text-lg font-bold text-slate-800 mb-2">{course.title}</h2>
            <p className="text-slate-600 text-sm leading-relaxed">{course.description}</p>
            <div className="flex items-center gap-4 mt-4 text-xs text-slate-500">
              <div className="flex items-center">
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
                <span>{course.chapters.length} 章节</span>
              </div>
              <div className="flex items-center">
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>{course.duration}</span>
              </div>
            </div>
          </div>

          {/* 章节列表 */}
          <div>
            <h3 className="text-lg font-semibold text-slate-800 mb-4">章节列表</h3>
            <div className="space-y-3">
              {course.chapters.map((chapter, index) => (
                <ChapterCard
                  key={chapter.id}
                  chapter={chapter}
                  index={index}
                  isActive={selectedChapter?.id === chapter.id}
                  onClick={() => handleChapterSelect(chapter)}
                  themeColors={themeColors}
                />
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* 右侧：章节内容区域 */}
      <div className="flex-1 fade-in min-h-screen" style={{ animationDelay: '100ms' }}>
        <div className="h-screen overflow-y-auto scrollbar-hide">
          {selectedChapter ? (
            <div
              key={selectedChapter.id}
              className="p-6 fade-in min-h-full"
            >
              {/* 章节标题 */}
              <div className={`solid-card p-6 mb-6 border-l-4 ${themeColors.border}`}>
                <div className="flex items-center space-x-4">
                  <div className={`w-12 h-12 ${themeColors.primary} text-white rounded-lg flex items-center justify-center font-bold`}>
                    {selectedChapter.id}
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-slate-800">{selectedChapter.title}</h1>
                    <p className="text-slate-600">{selectedChapter.description}</p>
                  </div>
                </div>
              </div>

              {/* 章节内容 */}
              <div className="solid-card p-0 overflow-hidden">
                <iframe
                  src={selectedChapter.htmlFile}
                  className="w-full border-0 rounded-lg"
                  style={{ height: 'calc(100vh)', minHeight: '800px' }}
                  title={selectedChapter.title}
                  loading="lazy"
                  onError={() => {
                    console.log('HTML文件加载失败，显示默认内容')
                  }}
                />
              </div>
            </div>
          ) : (
            <div className="h-full flex items-center justify-center">
              <div className="text-center text-slate-500">
                <div className="text-6xl mb-6">📚</div>
                <h3 className="text-xl font-semibold mb-2">选择章节开始学习</h3>
                <p className="text-slate-400">从左侧选择一个章节来查看详细内容</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
})

ChapterPage.displayName = 'ChapterPage'

export default ChapterPage
