import React, { useState, useEffect, useCallback, useMemo, memo } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import ChapterCard from '../components/ChapterCard'
import coursesData from '../data/courses.json'

const ChapterPage = memo(() => {
  const { courseId } = useParams()
  const navigate = useNavigate()
  const [selectedChapter, setSelectedChapter] = useState(null)

  const course = useMemo(() => {
    return coursesData.courses.find(c => c.id === parseInt(courseId))
  }, [courseId])

  useEffect(() => {
    if (course && course.chapters.length > 0) {
      setSelectedChapter(course.chapters[0]) // 默认选择第一个章节
    }
  }, [course])

  const handleChapterSelect = useCallback((chapter) => {
    setSelectedChapter(chapter)
  }, [])

  const handleBackToHome = useCallback(() => {
    navigate('/')
  }, [navigate])

  if (!course) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex flex-col lg:flex-row">
      {/* 左侧：课程卡片区域 */}
      <div className="lg:w-80 lg:min-h-screen bg-white border-r-2 border-slate-200 fade-in">

        <div className="p-6 lg:p-8 h-full overflow-y-auto scrollbar-hide">
          {/* 返回按钮 */}
          <button
            onClick={handleBackToHome}
            className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 mb-6 transition-colors duration-200 hover-lift"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            <span>返回首页</span>
          </button>

          {/* 课程信息卡片 */}
          <div className="solid-card-elevated p-6 mb-6 slide-up border-t-4 border-blue-600" style={{ animationDelay: '50ms' }}>
            <div className="aspect-video bg-blue-600 rounded-lg mb-4 flex items-center justify-center border-2 border-slate-200">
              <div className="text-white text-3xl font-bold">
                {course.title.charAt(0)}
              </div>
            </div>
            <h2 className="text-xl font-bold primary-text mb-2">{course.title}</h2>
            <p className="text-slate-600 text-sm">{course.description}</p>
          </div>

          {/* 章节列表 */}
          <div>
            <h3 className="text-lg font-semibold text-slate-800 mb-4">章节列表</h3>
            <div className="space-y-2">
              {course.chapters.map((chapter, index) => (
                <ChapterCard
                  key={chapter.id}
                  chapter={chapter}
                  index={index}
                  isActive={selectedChapter?.id === chapter.id}
                  onClick={() => handleChapterSelect(chapter)}
                />
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* 右侧：章节内容区域 */}
      <div className="flex-1 fade-in" style={{ animationDelay: '100ms' }}>

        <div className="h-full overflow-y-auto scrollbar-hide">
          {selectedChapter ? (
            <div
              key={selectedChapter.id}
              className="p-6 fade-in"
            >
              {/* 章节标题 */}
              <div className="solid-card-elevated p-6 mb-6 border-l-4 border-blue-600">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="w-12 h-12 bg-blue-600 text-white rounded-lg flex items-center justify-center font-bold border-2 border-slate-200">
                    {selectedChapter.id}
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold primary-text">{selectedChapter.title}</h1>
                    <p className="text-slate-600">{selectedChapter.description}</p>
                  </div>
                </div>
              </div>

              {/* 章节内容 */}
              <div className="solid-card p-6">
                <div className="prose max-w-none">
                  <iframe
                    src={selectedChapter.htmlFile}
                    className="w-full h-full border-0 rounded-lg"
                    title={selectedChapter.title}
                    loading="lazy"
                    onError={() => {
                      // 如果HTML文件不存在，显示默认内容
                      console.log('HTML文件加载失败，显示默认内容')
                    }}
                  />
                </div>
              </div>
            </div>
          ) : (
            <div className="h-full flex items-center justify-center">
              <div className="text-center text-slate-500">
                <div className="text-4xl mb-4">📚</div>
                <p>请选择一个章节开始学习</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
})

ChapterPage.displayName = 'ChapterPage'

export default ChapterPage
