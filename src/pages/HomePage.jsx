import React, { memo, useMemo } from 'react'
import CourseCard from '../components/CourseCard'
import coursesData from '../data/courses.json'

const HomePage = memo(() => {
  const courses = useMemo(() => coursesData.courses, [])

  return (
    <div className="min-h-screen">
      {/* 头部区域 */}
      <header className="relative overflow-hidden fade-in">
        {/* 背景装饰 */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-purple-50 to-green-50 opacity-60"></div>
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-10 left-10 w-20 h-20 bg-blue-200 rounded-full opacity-20 animate-pulse"></div>
          <div className="absolute top-20 right-20 w-16 h-16 bg-purple-200 rounded-full opacity-20 animate-pulse" style={{animationDelay: '1s'}}></div>
          <div className="absolute bottom-20 left-1/4 w-12 h-12 bg-green-200 rounded-full opacity-20 animate-pulse" style={{animationDelay: '2s'}}></div>
        </div>

        <div className="relative solid-card-elevated mx-4 mt-6 p-8 text-center border-t-4 border-gradient-to-r from-blue-600 via-purple-600 to-green-600">
          <div className="mb-6">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-600 to-purple-600 rounded-full mb-4">
              <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-green-600 bg-clip-text text-transparent mb-4">
              英语课程学习平台
            </h1>
            <p className="text-slate-600 text-lg max-w-2xl mx-auto mb-6">
              现代化的英语学习体验，系统性掌握英语语法、词汇和表达技巧
            </p>

            {/* 特色功能展示 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-4xl mx-auto">
              <div className="flex items-center justify-center space-x-2 bg-blue-50 p-3 rounded-lg">
                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
                <span className="text-blue-600 font-medium text-sm">智能学习</span>
              </div>
              <div className="flex items-center justify-center space-x-2 bg-purple-50 p-3 rounded-lg">
                <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                <span className="text-purple-600 font-medium text-sm">快速进步</span>
              </div>
              <div className="flex items-center justify-center space-x-2 bg-green-50 p-3 rounded-lg">
                <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-green-600 font-medium text-sm">系统认证</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* 统计信息区域 */}
      <section className="container mx-auto px-4 py-8 fade-in" style={{ animationDelay: '100ms' }}>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
          <div className="solid-card p-6 text-center">
            <div className="text-3xl font-bold text-blue-600 mb-2">{courses.length}</div>
            <div className="text-slate-600">精品课程</div>
          </div>
          <div className="solid-card p-6 text-center">
            <div className="text-3xl font-bold text-purple-600 mb-2">
              {courses.reduce((total, course) => total + course.chapters.length, 0)}
            </div>
            <div className="text-slate-600">学习章节</div>
          </div>
          <div className="solid-card p-6 text-center">
            <div className="text-3xl font-bold text-green-600 mb-2">100%</div>
            <div className="text-slate-600">免费学习</div>
          </div>
        </div>
      </section>

      {/* 课程展示区域 */}
      <main className="container mx-auto px-4 py-8">
        <div className="fade-in" style={{ animationDelay: '200ms' }}>
          <div className="mb-8 text-center">
            <h2 className="text-3xl font-bold text-slate-800 mb-4">精选课程</h2>
            <p className="text-slate-600 max-w-2xl mx-auto mb-6">
              从基础语法到高级应用，我们为您精心设计了完整的学习路径
            </p>
            <div className="w-24 h-1 bg-gradient-to-r from-blue-600 via-purple-600 to-green-600 rounded mx-auto"></div>
          </div>

          {/* 响应式网格布局 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
            {courses.map((course, index) => (
              <CourseCard
                key={course.id}
                course={course}
                index={index}
              />
            ))}
          </div>
        </div>
      </main>

      {/* 学习优势展示 */}
      <section className="container mx-auto px-4 py-16 fade-in" style={{ animationDelay: '300ms' }}>
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h3 className="text-2xl font-bold text-slate-800 mb-4">为什么选择我们？</h3>
            <p className="text-slate-600">专业的课程设计，让英语学习更高效</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="solid-card p-6 text-center group hover-lift">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-200 transition-colors">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <h4 className="font-semibold text-slate-800 mb-2">系统化课程</h4>
              <p className="text-sm text-slate-600">从基础到进阶，循序渐进的学习路径</p>
            </div>

            <div className="solid-card p-6 text-center group hover-lift">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:bg-purple-200 transition-colors">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <h4 className="font-semibold text-slate-800 mb-2">互动学习</h4>
              <p className="text-sm text-slate-600">丰富的练习和实时反馈机制</p>
            </div>

            <div className="solid-card p-6 text-center group hover-lift">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:bg-green-200 transition-colors">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h4 className="font-semibold text-slate-800 mb-2">快速提升</h4>
              <p className="text-sm text-slate-600">高效的学习方法，快速提升英语水平</p>
            </div>

            <div className="solid-card p-6 text-center group hover-lift">
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:bg-orange-200 transition-colors">
                <svg className="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h4 className="font-semibold text-slate-800 mb-2">灵活时间</h4>
              <p className="text-sm text-slate-600">随时随地学习，适应您的时间安排</p>
            </div>
          </div>
        </div>
      </section>

      {/* 底部装饰 */}
      <footer className="mt-16 pb-8 fade-in" style={{ animationDelay: '400ms' }}>
        <div className="container mx-auto px-4">
          <div className="solid-card p-8 text-center">
            <div className="mb-4">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-600 to-purple-600 rounded-full mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-slate-800 mb-2">开始您的英语学习之旅</h3>
              <p className="text-slate-600 mb-4">选择适合您的课程，立即开始学习</p>
            </div>
            <div className="text-center text-slate-500 text-sm border-t border-slate-200 pt-4">
              <p>© 2024 英语课程学习平台 - 现代化学习体验</p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
})

HomePage.displayName = 'HomePage'

export default HomePage
