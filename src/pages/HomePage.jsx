import React, { memo, useMemo } from 'react'
import CourseCard from '../components/CourseCard'
import coursesData from '../data/courses.json'

const HomePage = memo(() => {
  const courses = useMemo(() => coursesData.courses, [])

  return (
    <div className="min-h-screen">
      {/* 头部区域 */}
      <header className="relative overflow-hidden fade-in">
        <div className="solid-card-elevated mx-4 mt-6 p-8 text-center border-t-4 border-blue-600">
          <h1 className="text-4xl md:text-5xl font-bold primary-text mb-4">
            英语课程学习平台
          </h1>
          <p className="text-slate-600 text-lg max-w-2xl mx-auto">
            现代化的英语学习体验，系统性掌握英语语法、词汇和表达技巧
          </p>
        </div>
      </header>

      {/* 课程展示区域 */}
      <main className="container mx-auto px-4 py-8">
        <div className="fade-in" style={{ animationDelay: '100ms' }}>
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-slate-800 mb-2">课程列表</h2>
            <div className="w-16 h-1 bg-blue-600 rounded"></div>
          </div>

          {/* 响应式网格布局 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
            {courses.map((course, index) => (
              <CourseCard
                key={course.id}
                course={course}
                index={index}
              />
            ))}
          </div>
        </div>
      </main>

      {/* 底部装饰 */}
      <footer className="mt-16 pb-8 fade-in" style={{ animationDelay: '200ms' }}>
        <div className="text-center text-slate-500 text-sm">
          <p>© 2024 英语课程学习平台 - 现代化学习体验</p>
        </div>
      </footer>
    </div>
  )
})

HomePage.displayName = 'HomePage'

export default HomePage
