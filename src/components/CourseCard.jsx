import React, { memo, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'

const CourseCard = memo(({ course, index }) => {
  const navigate = useNavigate()

  const handleClick = useCallback(() => {
    navigate(`/course/${course.id}`)
  }, [navigate, course.id])

  // 获取主题颜色配置
  const getThemeColors = (theme) => {
    const themes = {
      blue: {
        primary: 'bg-blue-600',
        light: 'bg-blue-50',
        border: 'border-blue-600',
        text: 'text-blue-600',
        hover: 'group-hover:border-blue-300'
      },
      purple: {
        primary: 'bg-purple-600',
        light: 'bg-purple-50',
        border: 'border-purple-600',
        text: 'text-purple-600',
        hover: 'group-hover:border-purple-300'
      },
      green: {
        primary: 'bg-green-600',
        light: 'bg-green-50',
        border: 'border-green-600',
        text: 'text-green-600',
        hover: 'group-hover:border-green-300'
      },
      orange: {
        primary: 'bg-orange-600',
        light: 'bg-orange-50',
        border: 'border-orange-600',
        text: 'text-orange-600',
        hover: 'group-hover:border-orange-300'
      }
    }
    return themes[theme] || themes.blue
  }

  const themeColors = getThemeColors(course.theme)

  return (
    <div
      className="group cursor-pointer slide-up hover-lift"
      onClick={handleClick}
      style={{ animationDelay: `${index * 50}ms` }}
    >
      <div className="solid-card p-4 h-full hover:shadow-lg transition-all duration-300">
        {/* 课程封面 */}
        <div className={`relative mb-4 overflow-hidden rounded-lg border ${themeColors.border}`}>
          <div className={`aspect-square ${themeColors.primary} flex items-center justify-center`}>
            <div className="text-white text-2xl font-bold">
              {course.title.charAt(0)}
            </div>
          </div>
          <div className="absolute top-2 right-2">
            <div className={`px-2 py-1 ${themeColors.light} rounded text-xs font-medium ${themeColors.text}`}>
              {course.level}
            </div>
          </div>
        </div>

        {/* 课程标题 */}
        <h3 className="text-lg font-bold text-slate-800 mb-2">
          {course.title}
        </h3>

        {/* 课程描述 */}
        <p className="text-slate-600 text-sm mb-4 line-clamp-2 leading-relaxed">
          {course.description}
        </p>

        {/* 章节预览 - 左右结构 */}
        <div className="grid grid-cols-2 gap-3">
          <div className="space-y-1">
            {course.chapters.slice(0, 3).map((chapter, idx) => (
              <div key={chapter.id} className="flex items-center text-xs text-slate-600">
                <div className="w-4 h-4 rounded bg-slate-100 flex items-center justify-center mr-2 text-xs font-medium">
                  {idx + 1}
                </div>
                <span className="truncate">{chapter.title}</span>
              </div>
            ))}
          </div>
          <div className="space-y-1">
            {course.chapters.slice(3, 6).map((chapter, idx) => (
              <div key={chapter.id} className="flex items-center text-xs text-slate-600">
                <div className="w-4 h-4 rounded bg-slate-100 flex items-center justify-center mr-2 text-xs font-medium">
                  {idx + 4}
                </div>
                <span className="truncate">{chapter.title}</span>
              </div>
            ))}
            {course.chapters.length > 6 && (
              <div className="text-xs text-slate-500">
                +{course.chapters.length - 6} 更多
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
})

CourseCard.displayName = 'CourseCard'

export default CourseCard
