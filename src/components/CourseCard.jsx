import React, { memo, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'

const CourseCard = memo(({ course, index }) => {
  const navigate = useNavigate()

  const handleClick = useCallback(() => {
    navigate(`/course/${course.id}`)
  }, [navigate, course.id])

  return (
    <div
      className="group cursor-pointer slide-up hover-lift"
      onClick={handleClick}
      style={{ animationDelay: `${index * 50}ms` }}
    >
      <div className="solid-card p-6 h-full group-hover:border-blue-300 transition-all duration-300">
        {/* 课程封面 */}
        <div className="relative mb-4 overflow-hidden rounded-lg border-2 border-slate-200">
          <div className="aspect-video bg-blue-600 flex items-center justify-center">
            <div className="text-white text-4xl font-bold">
              {course.title.charAt(0)}
            </div>
          </div>
          <div className="absolute top-2 right-2">
            <div className="w-3 h-3 bg-white rounded-full opacity-80"></div>
          </div>
        </div>

        {/* 课程标题 */}
        <h3 className="text-xl font-bold text-slate-800 mb-2 primary-text">
          {course.title}
        </h3>

        {/* 课程描述 */}
        <p className="text-slate-600 text-sm mb-4 line-clamp-2">
          {course.description}
        </p>

        {/* 课程统计 */}
        <div className="flex items-center gap-3 mb-4">
          <div className="flex items-center bg-slate-100 px-3 py-1 rounded-full">
            <svg className="w-4 h-4 mr-1 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
            <span className="text-xs text-slate-600 font-medium">{course.chapters.length} 章节</span>
          </div>
          <div className="flex items-center bg-blue-100 px-3 py-1 rounded-full">
            <svg className="w-4 h-4 mr-1 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-xs text-blue-600 font-medium">{course.duration}</span>
          </div>
        </div>

        {/* 底部操作区 */}
        <div className="mt-auto pt-4 border-t border-slate-200">
          <div className="flex items-center justify-between">
            <span className="text-sm text-slate-600 font-medium">
              开始学习
            </span>
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 group-hover:scale-110">
              <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
})

CourseCard.displayName = 'CourseCard'

export default CourseCard
