import React, { memo, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'

const CourseCard = memo(({ course, index }) => {
  const navigate = useNavigate()

  const handleClick = useCallback(() => {
    navigate(`/course/${course.id}`)
  }, [navigate, course.id])

  // 获取主题颜色配置
  const getThemeColors = (theme) => {
    const themes = {
      blue: {
        primary: 'bg-blue-600',
        light: 'bg-blue-50',
        border: 'border-blue-600',
        text: 'text-blue-600',
        hover: 'group-hover:border-blue-300'
      },
      purple: {
        primary: 'bg-purple-600',
        light: 'bg-purple-50',
        border: 'border-purple-600',
        text: 'text-purple-600',
        hover: 'group-hover:border-purple-300'
      },
      green: {
        primary: 'bg-green-600',
        light: 'bg-green-50',
        border: 'border-green-600',
        text: 'text-green-600',
        hover: 'group-hover:border-green-300'
      },
      orange: {
        primary: 'bg-orange-600',
        light: 'bg-orange-50',
        border: 'border-orange-600',
        text: 'text-orange-600',
        hover: 'group-hover:border-orange-300'
      }
    }
    return themes[theme] || themes.blue
  }

  const themeColors = getThemeColors(course.theme)

  return (
    <div
      className="group cursor-pointer slide-up hover-lift"
      onClick={handleClick}
      style={{ animationDelay: `${index * 50}ms` }}
    >
      <div className="solid-card p-8 h-full hover:shadow-lg transition-all duration-300">
        {/* 课程封面 */}
        <div className={`relative mb-6 overflow-hidden rounded-lg border ${themeColors.border}`}>
          <div className={`aspect-video ${themeColors.primary} flex items-center justify-center`}>
            <div className="text-white text-3xl font-bold">
              {course.title.charAt(0)}
            </div>
          </div>
          <div className="absolute top-3 right-3">
            <div className={`px-3 py-1 ${themeColors.light} rounded-full text-xs font-medium ${themeColors.text}`}>
              {course.level}
            </div>
          </div>
        </div>

        {/* 课程标题 */}
        <h3 className="text-xl font-bold text-slate-800 mb-3">
          {course.title}
        </h3>

        {/* 课程描述 */}
        <p className="text-slate-600 text-sm mb-6 line-clamp-2 leading-relaxed">
          {course.description}
        </p>

        {/* 课程统计 */}
        <div className="flex items-center gap-4 mb-6">
          <div className="flex items-center text-slate-600">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
            <span className="text-sm font-medium">{course.chapters.length} 章节</span>
          </div>
          <div className="flex items-center text-slate-600">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-sm font-medium">{course.duration}</span>
          </div>
        </div>

        {/* 章节预览 */}
        <div className="mb-6">
          <h4 className="text-sm font-semibold text-slate-700 mb-3">课程章节</h4>
          <div className="space-y-2">
            {course.chapters.slice(0, 3).map((chapter, idx) => (
              <div key={chapter.id} className="flex items-center text-sm text-slate-600">
                <div className="w-5 h-5 rounded-full bg-slate-100 flex items-center justify-center mr-3 text-xs font-medium">
                  {idx + 1}
                </div>
                <span className="truncate">{chapter.title}</span>
              </div>
            ))}
            {course.chapters.length > 3 && (
              <div className="text-sm text-slate-500 pl-8">
                +{course.chapters.length - 3} 更多章节
              </div>
            )}
          </div>
        </div>

        {/* 底部操作区 */}
        <div className="mt-auto pt-6 border-t border-slate-100">
          <div className="flex items-center justify-between">
            <span className="text-sm text-slate-600 font-medium">
              开始学习
            </span>
            <div className="w-8 h-8 bg-slate-800 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200">
              <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
})

CourseCard.displayName = 'CourseCard'

export default CourseCard
