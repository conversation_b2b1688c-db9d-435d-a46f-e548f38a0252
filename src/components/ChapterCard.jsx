import React, { memo, useCallback } from 'react'

const ChapterCard = memo(({ chapter, isActive, onClick, index }) => {
  const handleClick = useCallback(() => {
    onClick(chapter)
  }, [onClick, chapter])
  return (
    <div
      className={`
        cursor-pointer p-4 rounded-lg transition-all duration-200 mb-3 fade-in hover-lift border-2
        ${isActive
          ? 'solid-card-elevated border-blue-600 bg-blue-50'
          : 'solid-card border-slate-200 hover:border-blue-300'
        }
      `}
      onClick={handleClick}
      style={{ animationDelay: `${index * 30}ms` }}
    >
      <div className="flex items-center space-x-3">
        {/* 章节编号 */}
        <div className={`
          w-10 h-10 rounded-lg flex items-center justify-center text-sm font-bold border-2
          ${isActive
            ? 'bg-blue-600 text-white border-blue-600'
            : 'bg-slate-100 text-slate-700 border-slate-200'
          }
        `}>
          {chapter.id}
        </div>

        {/* 章节信息 */}
        <div className="flex-1 min-w-0">
          <h4 className={`
            font-semibold text-sm truncate
            ${isActive ? 'text-blue-800' : 'text-slate-800'}
          `}>
            {chapter.title}
          </h4>
          <p className={`
            text-xs mt-1 line-clamp-2
            ${isActive ? 'text-blue-600' : 'text-slate-600'}
          `}>
            {chapter.description}
          </p>
        </div>

        {/* 活跃状态指示器 */}
        {isActive && (
          <div className="w-3 h-3 bg-blue-600 rounded-full border-2 border-white shadow-sm" />
        )}
      </div>
    </div>
  )
})

ChapterCard.displayName = 'ChapterCard'

export default ChapterCard
