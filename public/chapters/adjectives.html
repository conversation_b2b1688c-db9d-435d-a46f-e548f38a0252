<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>形容词</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #374151;
            margin: 0;
            padding: 20px;
            background: transparent;
        }
        .content {
            max-width: 800px;
            margin: 0 auto;
        }
        h1, h2, h3 {
            color: #1f2937;
            margin-top: 2rem;
            margin-bottom: 1rem;
        }
        h1 {
            font-size: 2rem;
            border-bottom: 3px solid #10b981;
            padding-bottom: 0.5rem;
        }
        h2 {
            font-size: 1.5rem;
            color: #10b981;
        }
        .example-box {
            background: #f3f4f6;
            border-left: 4px solid #10b981;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 8px 8px 0;
        }
        .rule {
            background: #d1fae5;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-weight: 600;
            margin: 0.5rem 0;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #d1d5db;
            padding: 0.5rem;
            text-align: left;
        }
        .comparison-table th {
            background: #f9fafb;
            font-weight: 600;
        }
        ul {
            padding-left: 1.5rem;
        }
        li {
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="content">
        <h1>形容词用法</h1>
        
        <h2>1. 形容词的位置</h2>
        
        <h3>定语位置</h3>
        <div class="rule">形容词 + 名词</div>
        <div class="example-box">
            <strong>例句：</strong>
            <ul>
                <li>a <strong>beautiful</strong> girl (一个美丽的女孩)</li>
                <li>a <strong>big</strong> house (一座大房子)</li>
                <li>an <strong>interesting</strong> book (一本有趣的书)</li>
            </ul>
        </div>

        <h3>表语位置</h3>
        <div class="rule">主语 + 系动词 + 形容词</div>
        <div class="example-box">
            <strong>例句：</strong>
            <ul>
                <li>She is <strong>beautiful</strong>. (她很美丽)</li>
                <li>The house is <strong>big</strong>. (房子很大)</li>
                <li>The book is <strong>interesting</strong>. (这本书很有趣)</li>
            </ul>
        </div>

        <h2>2. 形容词的比较级和最高级</h2>
        
        <table class="comparison-table">
            <thead>
                <tr>
                    <th>原级</th>
                    <th>比较级</th>
                    <th>最高级</th>
                    <th>规则</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>tall</td>
                    <td>taller</td>
                    <td>tallest</td>
                    <td>单音节词加 -er, -est</td>
                </tr>
                <tr>
                    <td>big</td>
                    <td>bigger</td>
                    <td>biggest</td>
                    <td>重读闭音节双写末尾字母</td>
                </tr>
                <tr>
                    <td>easy</td>
                    <td>easier</td>
                    <td>easiest</td>
                    <td>以y结尾变y为i加-er, -est</td>
                </tr>
                <tr>
                    <td>beautiful</td>
                    <td>more beautiful</td>
                    <td>most beautiful</td>
                    <td>多音节词用more, most</td>
                </tr>
                <tr>
                    <td>good</td>
                    <td>better</td>
                    <td>best</td>
                    <td>不规则变化</td>
                </tr>
            </tbody>
        </table>

        <h2>3. 形容词的排列顺序</h2>
        <div class="rule">限定词 + 观点 + 大小 + 年龄 + 形状 + 颜色 + 国籍 + 材料 + 名词</div>
        <div class="example-box">
            <strong>例句：</strong>
            <ul>
                <li>a <strong>beautiful small old round red Chinese wooden</strong> table</li>
                <li>two <strong>lovely little young</strong> girls</li>
                <li>a <strong>nice big new square</strong> room</li>
            </ul>
        </div>

        <h2>4. 常用形容词分类</h2>
        
        <h3>描述外观</h3>
        <ul>
            <li><strong>大小：</strong> big, small, large, tiny, huge</li>
            <li><strong>形状：</strong> round, square, long, short, wide</li>
            <li><strong>颜色：</strong> red, blue, green, yellow, black, white</li>
        </ul>

        <h3>描述性格</h3>
        <ul>
            <li><strong>积极：</strong> kind, friendly, honest, brave, patient</li>
            <li><strong>消极：</strong> rude, lazy, selfish, impatient, dishonest</li>
        </ul>

        <h3>描述感受</h3>
        <ul>
            <li><strong>情感：</strong> happy, sad, excited, nervous, surprised</li>
            <li><strong>感官：</strong> hot, cold, sweet, bitter, loud, quiet</li>
        </ul>

        <h2>练习要点</h2>
        <ul>
            <li>掌握形容词的基本位置规则</li>
            <li>熟练运用比较级和最高级</li>
            <li>注意多个形容词的排列顺序</li>
            <li>积累常用形容词词汇</li>
        </ul>
    </div>
</body>
</html>
