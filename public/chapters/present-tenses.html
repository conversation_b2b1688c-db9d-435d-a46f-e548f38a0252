<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>现在时态</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #374151;
            margin: 0;
            padding: 20px;
            background: transparent;
        }
        .content {
            max-width: 800px;
            margin: 0 auto;
        }
        h1, h2, h3 {
            color: #1f2937;
            margin-top: 2rem;
            margin-bottom: 1rem;
        }
        h1 {
            font-size: 2rem;
            border-bottom: 3px solid #8b5cf6;
            padding-bottom: 0.5rem;
            color: #8b5cf6;
        }
        h2 {
            font-size: 1.5rem;
            color: #8b5cf6;
            border-left: 4px solid #8b5cf6;
            padding-left: 1rem;
        }
        h3 {
            font-size: 1.2rem;
            color: #6d28d9;
        }
        .tense-box {
            background: linear-gradient(135deg, #ede9fe, #ddd6fe);
            border: 2px solid #8b5cf6;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 12px;
            font-weight: 600;
            text-align: center;
            font-size: 1.1rem;
        }
        .example-box {
            background: #f8fafc;
            border-left: 4px solid #10b981;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 8px 8px 0;
        }
        .usage-box {
            background: #fef3c7;
            border: 2px solid #f59e0b;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 8px;
        }
        .verb-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .verb-table th {
            background: #8b5cf6;
            color: white;
            padding: 1rem;
            text-align: left;
        }
        .verb-table td {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #e5e7eb;
        }
        .verb-table tr:nth-child(even) {
            background: #f9fafb;
        }
        .highlight {
            background: #a855f7;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        .time-indicator {
            background: #10b981;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        .structure-demo {
            display: flex;
            align-items: center;
            background: #f3e8ff;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            justify-content: center;
            flex-wrap: wrap;
        }
        .subject {
            background: #3b82f6;
            color: white;
            padding: 0.5rem;
            border-radius: 4px;
            margin: 0.25rem;
            font-weight: bold;
        }
        .verb {
            background: #8b5cf6;
            color: white;
            padding: 0.5rem;
            border-radius: 4px;
            margin: 0.25rem;
            font-weight: bold;
        }
        .object {
            background: #10b981;
            color: white;
            padding: 0.5rem;
            border-radius: 4px;
            margin: 0.25rem;
            font-weight: bold;
        }
        ul {
            padding-left: 1.5rem;
        }
        li {
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="content">
        <h1>2.1 现在时态</h1>
        
        <p>现在时态是英语中最基础也是最常用的时态。它包括简单现在时和现在进行时两种形式。</p>

        <h2>2.1.1 简单现在时</h2>
        
        <h3>基本结构</h3>
        <div class="tense-box">
            主语 + 动词原形/动词第三人称单数形式
        </div>

        <div class="structure-demo">
            <div class="subject">I/You/We/They</div>
            <div class="verb">work</div>
            <div class="object">every day</div>
        </div>

        <div class="structure-demo">
            <div class="subject">He/She/It</div>
            <div class="verb">works</div>
            <div class="object">every day</div>
        </div>

        <h3>第三人称单数变化规则</h3>
        <table class="verb-table">
            <thead>
                <tr>
                    <th>规则</th>
                    <th>原形</th>
                    <th>第三人称单数</th>
                    <th>例句</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>一般情况加-s</td>
                    <td>work, play, read</td>
                    <td>works, plays, reads</td>
                    <td>She <span class="highlight">works</span> hard.</td>
                </tr>
                <tr>
                    <td>以s,x,ch,sh,o结尾加-es</td>
                    <td>go, watch, fix</td>
                    <td>goes, watches, fixes</td>
                    <td>He <span class="highlight">goes</span> to school.</td>
                </tr>
                <tr>
                    <td>辅音+y结尾，变y为i加-es</td>
                    <td>study, try, fly</td>
                    <td>studies, tries, flies</td>
                    <td>She <span class="highlight">studies</span> English.</td>
                </tr>
                <tr>
                    <td>不规则变化</td>
                    <td>have, be</td>
                    <td>has, is</td>
                    <td>He <span class="highlight">has</span> a car.</td>
                </tr>
            </tbody>
        </table>

        <h3>用法</h3>
        <div class="usage-box">
            <strong>简单现在时的主要用法：</strong>
            <ul>
                <li><strong>习惯性动作：</strong>经常做的事情</li>
                <li><strong>客观事实：</strong>不变的真理</li>
                <li><strong>现在状态：</strong>目前的情况</li>
            </ul>
        </div>

        <div class="example-box">
            <strong>习惯性动作：</strong>
            <ul>
                <li>I <span class="highlight">get up</span> at 7 AM <span class="time-indicator">every day</span>. (我每天7点起床)</li>
                <li>She <span class="highlight">drinks</span> coffee <span class="time-indicator">every morning</span>. (她每天早上喝咖啡)</li>
                <li>We <span class="highlight">have</span> English class <span class="time-indicator">on Monday</span>. (我们周一有英语课)</li>
                <li>They <span class="highlight">play</span> basketball <span class="time-indicator">twice a week</span>. (他们每周打两次篮球)</li>
            </ul>
        </div>

        <div class="example-box">
            <strong>客观事实：</strong>
            <ul>
                <li>The sun <span class="highlight">rises</span> in the east. (太阳从东方升起)</li>
                <li>Water <span class="highlight">boils</span> at 100°C. (水在100度沸腾)</li>
                <li>Cats <span class="highlight">like</span> fish. (猫喜欢鱼)</li>
                <li>The earth <span class="highlight">moves</span> around the sun. (地球围绕太阳转)</li>
            </ul>
        </div>

        <div class="example-box">
            <strong>现在状态：</strong>
            <ul>
                <li>I <span class="highlight">am</span> a student. (我是学生)</li>
                <li>She <span class="highlight">lives</span> in Beijing. (她住在北京)</li>
                <li>He <span class="highlight">works</span> in a hospital. (他在医院工作)</li>
                <li>We <span class="highlight">know</span> each other. (我们认识彼此)</li>
            </ul>
        </div>

        <h2>2.1.2 现在进行时</h2>

        <h3>基本结构</h3>
        <div class="tense-box">
            主语 + am/is/are + 动词-ing
        </div>

        <div class="structure-demo">
            <div class="subject">I</div>
            <div class="verb">am working</div>
            <div class="object">now</div>
        </div>

        <div class="structure-demo">
            <div class="subject">He/She/It</div>
            <div class="verb">is working</div>
            <div class="object">now</div>
        </div>

        <div class="structure-demo">
            <div class="subject">You/We/They</div>
            <div class="verb">are working</div>
            <div class="object">now</div>
        </div>

        <h3>动词-ing形式变化规则</h3>
        <table class="verb-table">
            <thead>
                <tr>
                    <th>规则</th>
                    <th>原形</th>
                    <th>-ing形式</th>
                    <th>例句</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>一般情况直接加-ing</td>
                    <td>work, play, read</td>
                    <td>working, playing, reading</td>
                    <td>I am <span class="highlight">working</span>.</td>
                </tr>
                <tr>
                    <td>以不发音e结尾，去e加-ing</td>
                    <td>make, take, write</td>
                    <td>making, taking, writing</td>
                    <td>She is <span class="highlight">writing</span>.</td>
                </tr>
                <tr>
                    <td>重读闭音节，双写末尾辅音加-ing</td>
                    <td>run, sit, swim</td>
                    <td>running, sitting, swimming</td>
                    <td>He is <span class="highlight">running</span>.</td>
                </tr>
                <tr>
                    <td>以ie结尾，变ie为y加-ing</td>
                    <td>lie, die, tie</td>
                    <td>lying, dying, tying</td>
                    <td>The dog is <span class="highlight">lying</span>.</td>
                </tr>
            </tbody>
        </table>

        <h3>用法</h3>
        <div class="usage-box">
            <strong>现在进行时的主要用法：</strong>
            <ul>
                <li><strong>正在进行：</strong>现在正在做的事情</li>
                <li><strong>临时状态：</strong>暂时的情况</li>
                <li><strong>近期计划：</strong>已安排的将来行动</li>
            </ul>
        </div>

        <div class="example-box">
            <strong>正在进行的动作：</strong>
            <ul>
                <li>I <span class="highlight">am reading</span> a book <span class="time-indicator">now</span>. (我现在正在读书)</li>
                <li>She <span class="highlight">is cooking</span> dinner <span class="time-indicator">at the moment</span>. (她此刻正在做晚饭)</li>
                <li>They <span class="highlight">are playing</span> football <span class="time-indicator">right now</span>. (他们现在正在踢足球)</li>
                <li>We <span class="highlight">are watching</span> TV <span class="time-indicator">currently</span>. (我们目前正在看电视)</li>
            </ul>
        </div>

        <div class="example-box">
            <strong>临时状态：</strong>
            <ul>
                <li>I <span class="highlight">am staying</span> at a hotel this week. (我这周住在酒店)</li>
                <li>She <span class="highlight">is working</span> part-time this month. (她这个月在做兼职)</li>
                <li>He <span class="highlight">is learning</span> Chinese this year. (他今年在学中文)</li>
            </ul>
        </div>

        <div class="example-box">
            <strong>近期计划：</strong>
            <ul>
                <li>I <span class="highlight">am meeting</span> my friend tomorrow. (我明天要见朋友)</li>
                <li>We <span class="highlight">are going</span> to the movies tonight. (我们今晚要去看电影)</li>
                <li>She <span class="highlight">is flying</span> to London next week. (她下周要飞伦敦)</li>
            </ul>
        </div>

        <h3>时间标志词</h3>
        <div class="usage-box">
            <strong>简单现在时常用时间词：</strong>
            <ul>
                <li>always, usually, often, sometimes, never</li>
                <li>every day/week/month/year</li>
                <li>on Monday/Tuesday... (在星期几)</li>
                <li>in the morning/afternoon/evening</li>
            </ul>
        </div>

        <div class="usage-box">
            <strong>现在进行时常用时间词：</strong>
            <ul>
                <li>now, right now, at the moment</li>
                <li>currently, presently</li>
                <li>today, this week/month/year</li>
                <li>Look! Listen! (用于引起注意)</li>
            </ul>
        </div>
    </div>
</body>
</html>
