<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>形容词用法</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #374151;
            margin: 0;
            padding: 20px;
            background: transparent;
        }
        .content {
            max-width: 800px;
            margin: 0 auto;
        }
        h1, h2, h3 {
            color: #1f2937;
            margin-top: 2rem;
            margin-bottom: 1rem;
        }
        h1 {
            font-size: 2rem;
            border-bottom: 3px solid #3b82f6;
            padding-bottom: 0.5rem;
            color: #3b82f6;
        }
        h2 {
            font-size: 1.5rem;
            color: #3b82f6;
            border-left: 4px solid #3b82f6;
            padding-left: 1rem;
        }
        h3 {
            font-size: 1.2rem;
            color: #1e40af;
        }
        .pattern-box {
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
            border: 2px solid #3b82f6;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 12px;
            font-weight: 600;
            text-align: center;
            font-size: 1.1rem;
        }
        .example-box {
            background: #f8fafc;
            border-left: 4px solid #10b981;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 8px 8px 0;
        }
        .order-box {
            background: #fef3c7;
            border: 2px solid #f59e0b;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 8px;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .comparison-table th {
            background: #3b82f6;
            color: white;
            padding: 1rem;
            text-align: left;
        }
        .comparison-table td {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #e5e7eb;
        }
        .comparison-table tr:nth-child(even) {
            background: #f9fafb;
        }
        .highlight {
            background: #fbbf24;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        .position-demo {
            display: flex;
            align-items: center;
            background: #e0e7ff;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            justify-content: center;
            flex-wrap: wrap;
        }
        .adj-before {
            background: #fbbf24;
            padding: 0.5rem;
            border-radius: 4px;
            margin: 0.25rem;
            font-weight: bold;
        }
        .noun {
            background: #10b981;
            color: white;
            padding: 0.5rem;
            border-radius: 4px;
            margin: 0.25rem;
            font-weight: bold;
        }
        .verb {
            background: #8b5cf6;
            color: white;
            padding: 0.5rem;
            border-radius: 4px;
            margin: 0.25rem;
            font-weight: bold;
        }
        .adj-after {
            background: #f59e0b;
            color: white;
            padding: 0.5rem;
            border-radius: 4px;
            margin: 0.25rem;
            font-weight: bold;
        }
        ul {
            padding-left: 1.5rem;
        }
        li {
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="content">
        <h1>1.2 形容词用法</h1>
        
        <p>形容词用来描述名词的特征、性质或状态。掌握形容词的位置、顺序和比较级用法是理解英语句子的重要技能。</p>

        <h2>1.2.1 形容词位置</h2>
        
        <h3>位置一：名词前（定语）</h3>
        <div class="pattern-box">
            形容词 + 名词
        </div>

        <div class="position-demo">
            <div class="adj-before">beautiful</div>
            <div class="noun">girl</div>
        </div>
        
        <div class="example-box">
            <strong>例句：</strong>
            <ul>
                <li>a <span class="highlight">red</span> car (一辆红色的车)</li>
                <li>a <span class="highlight">big</span> house (一座大房子)</li>
                <li>an <span class="highlight">interesting</span> book (一本有趣的书)</li>
                <li>a <span class="highlight">young</span> teacher (一位年轻的老师)</li>
                <li><span class="highlight">fresh</span> air (新鲜空气)</li>
            </ul>
        </div>

        <h3>位置二：系动词后（表语）</h3>
        <div class="pattern-box">
            主语 + 系动词 + 形容词
        </div>

        <div class="position-demo">
            <div class="noun">The girl</div>
            <div class="verb">is</div>
            <div class="adj-after">beautiful</div>
        </div>
        
        <div class="example-box">
            <strong>常用系动词：</strong> be, look, sound, feel, taste, smell, seem, become, get
            <ul>
                <li>The car <span class="highlight">is</span> red. (车是红色的)</li>
                <li>The house <span class="highlight">looks</span> big. (房子看起来很大)</li>
                <li>The book <span class="highlight">seems</span> interesting. (这本书似乎很有趣)</li>
                <li>She <span class="highlight">became</span> famous. (她变得出名了)</li>
                <li>The music <span class="highlight">sounds</span> beautiful. (音乐听起来很美)</li>
            </ul>
        </div>

        <h2>1.2.2 形容词顺序</h2>
        
        <p>当多个形容词修饰同一个名词时，它们有固定的顺序：</p>
        
        <div class="order-box">
            <strong>形容词顺序记忆口诀：</strong><br>
            <strong>数量 → 品质 → 大小 → 形状 → 年龄 → 颜色 → 国籍 → 材料 → 名词</strong>
        </div>

        <table class="comparison-table">
            <thead>
                <tr>
                    <th>顺序</th>
                    <th>类型</th>
                    <th>例词</th>
                    <th>示例</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>1</td>
                    <td>数量</td>
                    <td>one, two, many, few</td>
                    <td>two beautiful girls</td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>品质/观点</td>
                    <td>beautiful, nice, good</td>
                    <td>a beautiful big house</td>
                </tr>
                <tr>
                    <td>3</td>
                    <td>大小</td>
                    <td>big, small, large, tiny</td>
                    <td>a big round table</td>
                </tr>
                <tr>
                    <td>4</td>
                    <td>形状</td>
                    <td>round, square, long</td>
                    <td>a long wooden stick</td>
                </tr>
                <tr>
                    <td>5</td>
                    <td>年龄</td>
                    <td>old, new, young, ancient</td>
                    <td>an old red car</td>
                </tr>
                <tr>
                    <td>6</td>
                    <td>颜色</td>
                    <td>red, blue, green, black</td>
                    <td>a red Chinese flag</td>
                </tr>
                <tr>
                    <td>7</td>
                    <td>国籍/来源</td>
                    <td>Chinese, American, European</td>
                    <td>a Chinese silk dress</td>
                </tr>
                <tr>
                    <td>8</td>
                    <td>材料</td>
                    <td>wooden, plastic, metal</td>
                    <td>a wooden dining table</td>
                </tr>
            </tbody>
        </table>

        <div class="example-box">
            <strong>完整例句：</strong>
            <ul>
                <li>a <span class="highlight">beautiful big old red Chinese wooden</span> box</li>
                <li>two <span class="highlight">nice small round new blue plastic</span> toys</li>
                <li>many <span class="highlight">interesting long old English</span> stories</li>
            </ul>
        </div>

        <h2>1.2.3 比较级与最高级</h2>

        <h3>比较级（Comparative）</h3>
        <div class="pattern-box">
            A + be + 形容词比较级 + than + B
        </div>

        <table class="comparison-table">
            <thead>
                <tr>
                    <th>原级</th>
                    <th>比较级</th>
                    <th>最高级</th>
                    <th>例句</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>tall</td>
                    <td>taller</td>
                    <td>tallest</td>
                    <td>He is taller than me.</td>
                </tr>
                <tr>
                    <td>big</td>
                    <td>bigger</td>
                    <td>biggest</td>
                    <td>This room is bigger than that one.</td>
                </tr>
                <tr>
                    <td>beautiful</td>
                    <td>more beautiful</td>
                    <td>most beautiful</td>
                    <td>She is more beautiful than her sister.</td>
                </tr>
                <tr>
                    <td>good</td>
                    <td>better</td>
                    <td>best</td>
                    <td>This book is better than that one.</td>
                </tr>
                <tr>
                    <td>bad</td>
                    <td>worse</td>
                    <td>worst</td>
                    <td>Today is worse than yesterday.</td>
                </tr>
            </tbody>
        </table>

        <h3>最高级（Superlative）</h3>
        <div class="pattern-box">
            A + be + the + 形容词最高级 + (in/of + 范围)
        </div>

        <div class="example-box">
            <strong>例句：</strong>
            <ul>
                <li>He is <span class="highlight">the tallest</span> student in our class. (他是我们班最高的学生)</li>
                <li>This is <span class="highlight">the most beautiful</span> place I've ever seen. (这是我见过的最美的地方)</li>
                <li>She is <span class="highlight">the best</span> teacher in the school. (她是学校里最好的老师)</li>
                <li>This is <span class="highlight">the worst</span> movie of the year. (这是今年最糟糕的电影)</li>
            </ul>
        </div>

        <div class="order-box">
            <strong>💡 形容词变化规则：</strong>
            <ul>
                <li><strong>单音节词：</strong> 直接加 -er/-est (tall → taller → tallest)</li>
                <li><strong>以e结尾：</strong> 加 -r/-st (nice → nicer → nicest)</li>
                <li><strong>辅音+y结尾：</strong> 变y为i加 -er/-est (happy → happier → happiest)</li>
                <li><strong>重读闭音节：</strong> 双写末尾辅音加 -er/-est (big → bigger → biggest)</li>
                <li><strong>多音节词：</strong> 前面加 more/most (beautiful → more beautiful → most beautiful)</li>
                <li><strong>不规则变化：</strong> 需要记忆 (good → better → best)</li>
            </ul>
        </div>
    </div>
</body>
</html>
