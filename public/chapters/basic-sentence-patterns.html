<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基本句型</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #374151;
            margin: 0;
            padding: 20px;
            background: transparent;
        }
        .content {
            max-width: 800px;
            margin: 0 auto;
        }
        h1, h2, h3 {
            color: #1f2937;
            margin-top: 2rem;
            margin-bottom: 1rem;
        }
        h1 {
            font-size: 2rem;
            border-bottom: 3px solid #3b82f6;
            padding-bottom: 0.5rem;
            color: #3b82f6;
        }
        h2 {
            font-size: 1.5rem;
            color: #3b82f6;
            border-left: 4px solid #3b82f6;
            padding-left: 1rem;
        }
        h3 {
            font-size: 1.2rem;
            color: #1e40af;
        }
        .pattern-box {
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
            border: 2px solid #3b82f6;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 12px;
            font-weight: 600;
            text-align: center;
            font-size: 1.1rem;
        }
        .example-box {
            background: #f8fafc;
            border-left: 4px solid #10b981;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 8px 8px 0;
        }
        .tip-box {
            background: #fef3c7;
            border: 2px solid #f59e0b;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 8px;
        }
        .highlight {
            background: #fbbf24;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        ul {
            padding-left: 1.5rem;
        }
        li {
            margin-bottom: 0.5rem;
        }
        .sentence-structure {
            display: flex;
            justify-content: space-around;
            background: #e0e7ff;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            flex-wrap: wrap;
        }
        .structure-part {
            background: white;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            margin: 0.25rem;
            border: 2px solid #6366f1;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="content">
        <h1>1.1 基本句型</h1>
        
        <p>英语句子有三种基本类型：<strong>陈述句</strong>、<strong>疑问句</strong>和<strong>祈使句</strong>。掌握这些基本句型是理解英语的第一步。</p>

        <h2>1.1.1 陈述句（主语 + 谓语 + 宾语/补语）</h2>
        
        <h3>基本结构</h3>
        <div class="pattern-box">
            主语 (Subject) + 谓语 (Verb) + 宾语/补语 (Object/Complement)
        </div>

        <div class="sentence-structure">
            <div class="structure-part">主语 (S)</div>
            <div class="structure-part">谓语 (V)</div>
            <div class="structure-part">宾语 (O)</div>
        </div>

        <h3>类型一：主语 + 谓语 (S + V)</h3>
        <div class="example-box">
            <strong>例句：</strong>
            <ul>
                <li><span class="highlight">Birds</span> <span class="highlight">fly</span>. (鸟儿飞翔)</li>
                <li><span class="highlight">She</span> <span class="highlight">laughs</span>. (她笑了)</li>
                <li><span class="highlight">Time</span> <span class="highlight">passes</span>. (时间流逝)</li>
                <li><span class="highlight">The sun</span> <span class="highlight">rises</span>. (太阳升起)</li>
            </ul>
        </div>

        <h3>类型二：主语 + 谓语 + 宾语 (S + V + O)</h3>
        <div class="example-box">
            <strong>例句：</strong>
            <ul>
                <li><span class="highlight">I</span> <span class="highlight">love</span> <span class="highlight">music</span>. (我喜欢音乐)</li>
                <li><span class="highlight">She</span> <span class="highlight">reads</span> <span class="highlight">books</span>. (她读书)</li>
                <li><span class="highlight">They</span> <span class="highlight">play</span> <span class="highlight">football</span>. (他们踢足球)</li>
                <li><span class="highlight">We</span> <span class="highlight">watch</span> <span class="highlight">movies</span>. (我们看电影)</li>
            </ul>
        </div>

        <h3>类型三：主语 + 系动词 + 表语 (S + V + C)</h3>
        <div class="example-box">
            <strong>例句：</strong>
            <ul>
                <li><span class="highlight">She</span> <span class="highlight">is</span> <span class="highlight">beautiful</span>. (她很漂亮)</li>
                <li><span class="highlight">The food</span> <span class="highlight">tastes</span> <span class="highlight">good</span>. (食物很好吃)</li>
                <li><span class="highlight">He</span> <span class="highlight">became</span> <span class="highlight">a doctor</span>. (他成为了医生)</li>
                <li><span class="highlight">The weather</span> <span class="highlight">looks</span> <span class="highlight">nice</span>. (天气看起来不错)</li>
            </ul>
        </div>

        <h2>1.1.2 疑问句</h2>

        <h3>一般疑问句</h3>
        <div class="pattern-box">
            助动词/系动词 + 主语 + 谓语 + 其他？
        </div>
        
        <div class="example-box">
            <strong>例句：</strong>
            <ul>
                <li><span class="highlight">Do</span> you like coffee? (你喜欢咖啡吗？)</li>
                <li><span class="highlight">Is</span> she a teacher? (她是老师吗？)</li>
                <li><span class="highlight">Can</span> you speak English? (你会说英语吗？)</li>
                <li><span class="highlight">Have</span> you finished your homework? (你完成作业了吗？)</li>
            </ul>
        </div>

        <h3>特殊疑问句</h3>
        <div class="pattern-box">
            疑问词 + 助动词/系动词 + 主语 + 谓语 + 其他？
        </div>
        
        <div class="example-box">
            <strong>常用疑问词：</strong>
            <ul>
                <li><strong>What</strong> - 什么：<span class="highlight">What</span> do you want? (你想要什么？)</li>
                <li><strong>Where</strong> - 哪里：<span class="highlight">Where</span> are you going? (你要去哪里？)</li>
                <li><strong>When</strong> - 什么时候：<span class="highlight">When</span> will you come? (你什么时候来？)</li>
                <li><strong>Why</strong> - 为什么：<span class="highlight">Why</span> are you late? (你为什么迟到？)</li>
                <li><strong>How</strong> - 怎么样：<span class="highlight">How</span> are you? (你好吗？)</li>
                <li><strong>Who</strong> - 谁：<span class="highlight">Who</span> is that person? (那个人是谁？)</li>
            </ul>
        </div>

        <h2>1.1.3 祈使句（动词原形开头）</h2>
        
        <div class="pattern-box">
            动词原形 + 其他 (省略主语you)
        </div>

        <h3>肯定祈使句</h3>
        <div class="example-box">
            <strong>例句：</strong>
            <ul>
                <li><span class="highlight">Sit</span> down, please. (请坐下)</li>
                <li><span class="highlight">Open</span> the window. (打开窗户)</li>
                <li><span class="highlight">Listen</span> carefully. (仔细听)</li>
                <li><span class="highlight">Be</span> quiet! (安静！)</li>
            </ul>
        </div>

        <h3>否定祈使句</h3>
        <div class="pattern-box">
            Don't + 动词原形 + 其他
        </div>
        
        <div class="example-box">
            <strong>例句：</strong>
            <ul>
                <li><span class="highlight">Don't</span> worry. (别担心)</li>
                <li><span class="highlight">Don't</span> be late. (别迟到)</li>
                <li><span class="highlight">Don't</span> forget your keys. (别忘了你的钥匙)</li>
                <li><span class="highlight">Don't</span> touch that! (别碰那个！)</li>
            </ul>
        </div>

        <div class="tip-box">
            <strong>💡 学习小贴士：</strong>
            <ul>
                <li>陈述句是最常见的句型，用来表达事实或观点</li>
                <li>疑问句用来询问信息，注意语调要上升</li>
                <li>祈使句用来给出指令或建议，通常省略主语"you"</li>
                <li>掌握这三种基本句型，就能理解大部分英语句子的结构</li>
            </ul>
        </div>
    </div>
</body>
</html>
