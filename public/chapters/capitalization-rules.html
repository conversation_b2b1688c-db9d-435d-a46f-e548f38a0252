<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大小写规则</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #374151;
            margin: 0;
            padding: 20px;
            background: transparent;
        }
        .content {
            max-width: 800px;
            margin: 0 auto;
        }
        h1, h2, h3 {
            color: #1f2937;
            margin-top: 2rem;
            margin-bottom: 1rem;
        }
        h1 {
            font-size: 2rem;
            border-bottom: 3px solid #3b82f6;
            padding-bottom: 0.5rem;
            color: #3b82f6;
        }
        h2 {
            font-size: 1.5rem;
            color: #3b82f6;
            border-left: 4px solid #3b82f6;
            padding-left: 1rem;
        }
        h3 {
            font-size: 1.2rem;
            color: #1e40af;
        }
        .rule-box {
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
            border: 2px solid #3b82f6;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 12px;
            font-weight: 600;
        }
        .example-box {
            background: #f8fafc;
            border-left: 4px solid #10b981;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 8px 8px 0;
        }
        .wrong-example {
            background: #fef2f2;
            border-left: 4px solid #ef4444;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 8px 8px 0;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .comparison-table th {
            background: #3b82f6;
            color: white;
            padding: 1rem;
            text-align: left;
        }
        .comparison-table td {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #e5e7eb;
        }
        .comparison-table tr:nth-child(even) {
            background: #f9fafb;
        }
        .highlight-correct {
            background: #10b981;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        .highlight-wrong {
            background: #ef4444;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        .capital-demo {
            font-size: 1.2rem;
            font-weight: bold;
            text-align: center;
            background: #e0e7ff;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        .capital-letter {
            color: #dc2626;
            font-size: 1.5rem;
        }
        ul {
            padding-left: 1.5rem;
        }
        li {
            margin-bottom: 0.5rem;
        }
        .tip-box {
            background: #fef3c7;
            border: 2px solid #f59e0b;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="content">
        <h1>1.3 大小写规则</h1>
        
        <p>英语的大小写规则是正确书写的基础。掌握这些规则能让你的英语写作更加规范和专业。</p>

        <h2>1.3.1 句子开头大写</h2>
        
        <div class="rule-box">
            <strong>规则：</strong>每个句子的第一个字母必须大写
        </div>

        <div class="capital-demo">
            <span class="capital-letter">T</span>he sun is shining today.
        </div>

        <div class="example-box">
            <strong>✅ 正确示例：</strong>
            <ul>
                <li><span class="highlight-correct">T</span>he weather is nice today.</li>
                <li><span class="highlight-correct">S</span>he loves reading books.</li>
                <li><span class="highlight-correct">W</span>e are going to the park.</li>
                <li><span class="highlight-correct">H</span>ello, how are you?</li>
                <li><span class="highlight-correct">G</span>ood morning, everyone!</li>
            </ul>
        </div>

        <div class="wrong-example">
            <strong>❌ 错误示例：</strong>
            <ul>
                <li><span class="highlight-wrong">t</span>he weather is nice today.</li>
                <li><span class="highlight-wrong">s</span>he loves reading books.</li>
                <li><span class="highlight-wrong">w</span>e are going to the park.</li>
            </ul>
        </div>

        <h3>特殊情况：句号、问号、感叹号后</h3>
        <div class="example-box">
            <strong>例句：</strong>
            <ul>
                <li>I like coffee. <span class="highlight-correct">S</span>he likes tea.</li>
                <li>Are you ready? <span class="highlight-correct">L</span>et's go!</li>
                <li>What a beautiful day! <span class="highlight-correct">I</span> feel so happy.</li>
            </ul>
        </div>

        <h2>1.3.2 专有名词、星期、月份大写</h2>

        <h3>人名和地名</h3>
        <div class="rule-box">
            <strong>规则：</strong>所有专有名词的首字母都要大写
        </div>

        <table class="comparison-table">
            <thead>
                <tr>
                    <th>类型</th>
                    <th>正确写法</th>
                    <th>错误写法</th>
                    <th>中文含义</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>人名</td>
                    <td>John, Mary, Smith</td>
                    <td>john, mary, smith</td>
                    <td>约翰，玛丽，史密斯</td>
                </tr>
                <tr>
                    <td>城市</td>
                    <td>Beijing, London, New York</td>
                    <td>beijing, london, new york</td>
                    <td>北京，伦敦，纽约</td>
                </tr>
                <tr>
                    <td>国家</td>
                    <td>China, America, England</td>
                    <td>china, america, england</td>
                    <td>中国，美国，英国</td>
                </tr>
                <tr>
                    <td>语言</td>
                    <td>English, Chinese, French</td>
                    <td>english, chinese, french</td>
                    <td>英语，中文，法语</td>
                </tr>
                <tr>
                    <td>公司</td>
                    <td>Apple, Google, Microsoft</td>
                    <td>apple, google, microsoft</td>
                    <td>苹果，谷歌，微软</td>
                </tr>
            </tbody>
        </table>

        <h3>星期和月份</h3>
        <div class="rule-box">
            <strong>规则：</strong>星期和月份的首字母必须大写
        </div>

        <table class="comparison-table">
            <thead>
                <tr>
                    <th>星期</th>
                    <th>正确写法</th>
                    <th>月份</th>
                    <th>正确写法</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>星期一</td>
                    <td>Monday</td>
                    <td>一月</td>
                    <td>January</td>
                </tr>
                <tr>
                    <td>星期二</td>
                    <td>Tuesday</td>
                    <td>二月</td>
                    <td>February</td>
                </tr>
                <tr>
                    <td>星期三</td>
                    <td>Wednesday</td>
                    <td>三月</td>
                    <td>March</td>
                </tr>
                <tr>
                    <td>星期四</td>
                    <td>Thursday</td>
                    <td>四月</td>
                    <td>April</td>
                </tr>
                <tr>
                    <td>星期五</td>
                    <td>Friday</td>
                    <td>五月</td>
                    <td>May</td>
                </tr>
                <tr>
                    <td>星期六</td>
                    <td>Saturday</td>
                    <td>六月</td>
                    <td>June</td>
                </tr>
                <tr>
                    <td>星期日</td>
                    <td>Sunday</td>
                    <td>七月</td>
                    <td>July</td>
                </tr>
            </tbody>
        </table>

        <div class="example-box">
            <strong>例句：</strong>
            <ul>
                <li>I will visit <span class="highlight-correct">Beijing</span> next <span class="highlight-correct">Monday</span>.</li>
                <li>She was born in <span class="highlight-correct">December</span>.</li>
                <li><span class="highlight-correct">Christmas</span> is on <span class="highlight-correct">December</span> 25th.</li>
                <li>We have English class on <span class="highlight-correct">Wednesday</span>.</li>
            </ul>
        </div>

        <h2>1.3.3 第一人称 I 大写</h2>

        <div class="rule-box">
            <strong>规则：</strong>第一人称单数 "I" 在任何位置都必须大写
        </div>

        <div class="capital-demo">
            <span class="capital-letter">I</span> love you and you love me.
        </div>

        <div class="example-box">
            <strong>✅ 正确示例：</strong>
            <ul>
                <li><span class="highlight-correct">I</span> am a student.</li>
                <li>My friend and <span class="highlight-correct">I</span> went to the movies.</li>
                <li>Can <span class="highlight-correct">I</span> help you?</li>
                <li>When <span class="highlight-correct">I</span> was young, <span class="highlight-correct">I</span> lived in Shanghai.</li>
                <li><span class="highlight-correct">I</span> think <span class="highlight-correct">I</span> can do it.</li>
            </ul>
        </div>

        <div class="wrong-example">
            <strong>❌ 错误示例：</strong>
            <ul>
                <li><span class="highlight-wrong">i</span> am a student.</li>
                <li>My friend and <span class="highlight-wrong">i</span> went to the movies.</li>
                <li>Can <span class="highlight-wrong">i</span> help you?</li>
            </ul>
        </div>

        <h3>其他需要大写的情况</h3>
        <div class="example-box">
            <strong>节日名称：</strong>
            <ul>
                <li><span class="highlight-correct">Christmas</span> (圣诞节)</li>
                <li><span class="highlight-correct">New Year</span> (新年)</li>
                <li><span class="highlight-correct">Easter</span> (复活节)</li>
                <li><span class="highlight-correct">Halloween</span> (万圣节)</li>
            </ul>
        </div>

        <div class="example-box">
            <strong>书名、电影名：</strong>
            <ul>
                <li><span class="highlight-correct">Harry Potter</span></li>
                <li><span class="highlight-correct">The Lion King</span></li>
                <li><span class="highlight-correct">Titanic</span></li>
            </ul>
        </div>

        <div class="tip-box">
            <strong>💡 记忆小贴士：</strong>
            <ul>
                <li>句子开头永远大写 - 这是最基本的规则</li>
                <li>"I" 无论在哪里都要大写 - 这是英语的特殊规则</li>
                <li>专有名词都大写 - 人名、地名、公司名等</li>
                <li>时间相关的词大写 - 星期、月份、节日</li>
                <li>当不确定时，查字典确认是否为专有名词</li>
            </ul>
        </div>

        <h3>练习检查</h3>
        <div class="wrong-example">
            <strong>找出错误并改正：</strong>
            <ul>
                <li>i live in beijing, china.</li>
                <li>my birthday is in january.</li>
                <li>we will meet on monday morning.</li>
                <li>she speaks english and chinese.</li>
            </ul>
        </div>

        <div class="example-box">
            <strong>正确答案：</strong>
            <ul>
                <li><span class="highlight-correct">I</span> live in <span class="highlight-correct">Beijing</span>, <span class="highlight-correct">China</span>.</li>
                <li><span class="highlight-correct">M</span>y birthday is in <span class="highlight-correct">January</span>.</li>
                <li><span class="highlight-correct">W</span>e will meet on <span class="highlight-correct">Monday</span> morning.</li>
                <li><span class="highlight-correct">S</span>he speaks <span class="highlight-correct">English</span> and <span class="highlight-correct">Chinese</span>.</li>
            </ul>
        </div>
    </div>
</body>
</html>
